<div class="fixed-theme-header">
    <div class="discount-header text-white">
        <div class="container d-flex justify-content-between align-items-center py-5">
            <p class="m-0 fs-13 nunito-sans regular">
                🌟 Get 5% Off your first order. Promo Code:
                <span class="extra-bold">ORDER5</span>
            </p>
            <div class="d-flex gap-4">
                <a href="mailto:{{ setting()->email ?? '' }}" class="fs-13 text-white regular">
                    <i class="fa-solid fa-envelope me-2"></i> {{ setting()->email ?? '' }}
                </a>
                <a href="tel:{{ setting()->phone ?? '' }}" class="fs-13 text-white regular">
                    <i class="fa-solid fa-phone me-2"></i> {{ setting()->phone ?? '' }}
                </a>
            </div>
        </div>
    </div>

    <!-- Main Navbar -->
    <nav class="navbar navbar-expand-lg bg-white header">
        <div class="container py-2">
            <!-- Logo -->
            <div class="d-flex gap-2 w-400px align-items-center">
                <a class="navbar-brand" href="{{ url('/') }}">
                    <img src="{{ asset('website') . '/' . setting()->logo ?? '' }}" alt="Logo"
                        class="h-35px w-30px">
                    <!-- <img src="{{ asset('website') }}/assets/images/logo-image.svg" alt="Logo" class="h-45px w-45px"> -->
                </a>

                <!-- Search Bar -->
                <form class="d-flex w-100 form-control rounded-pill align-items-center header-search h-50">
                    <i class="fa-solid fa-magnifying-glass me-3"></i>
                    <label for="searchInputToday" class="visually-hidden">Services Lookin for today</label>
                    <input class="w-100 fs-14 normal sora" type="search"
                        placeholder="What service are you looking for today?" name="searchInputToday"
                        id="searchInputToday" aria-label="Search Today">
                </form>
            </div>

            <!-- Toggler for mobile -->
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#mainNavbar">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse flex-end align-items-center navbar-header gap-4" id="mainNavbar">
                @auth
                    <div class="app-navbar-item ms-1 ">
                        <div class="btn btn-icon btn-custom btn-icon-muted btn-active-light btn-active-color-primary w-35px h-35px"
                            data-kt-menu-trigger="{default: 'click', lg: 'hover'}" data-kt-menu-attach="parent"
                            data-kt-menu-placement="bottom-end" id="kt_menu_item_wow">
                            <i class="far fa-bell"></i>
                            <span class="path1"></span>
                            <span class="path2"></span>
                            <span class="path3"></span>
                            <span class="path4"></span>
                            </i>
                        </div>
                        <div class="menu notification-dropdown menu-sub menu-sub-dropdown menu-column w-350px w-lg-375px p-5"
                            data-kt-menu="true" id="kt_menu_notifications">

                            <p class="fs-16 bold">Notifications</p>

                            @for ($i = 0; $i < 4; $i++)
                                <div
                                    class="d-flex align-items-center gap-3 justify-content-center  border-bottom mb-5 pb-3">
                                    <img src="{{ asset('website') }}/assets/images/notification-user.png" alt="Logo"
                                        class="h-50px">
                                    <div>
                                        <p class="fs-13 mb-0 light-black"><span class="semi_bold ">Jenny Wilson</span> Lorem
                                            Ipsum is simply dummy text of the printing.</p>
                                        <p class="fs-12 mb-0 ">5 min</p>
                                    </div>
                                </div>
                            @endfor

                            <a href="{{ route('notification') }}" class="see-all-btn"> See All</a>
                        </div>
                    </div>
                    @if (auth()->user()->hasRole('customer'))
                        <div lass="app-navbar-item ms-1">
                            <i class="far fa-envelope mt-1"></i>
                        </div>

                        <div lass="app-navbar-item ms-1">
                            <i class="far fa-question-circle mt-1"></i>
                        </div>
                        <div>
                            <a href="{{ route('favorite_professional') }}"
                                class="@if (request()->is('favorite_professional')) active-fav @endif" aria-label="Favourite">
                                <i class="fa-regular fa-heart"></i>
                            </a>
                        </div>
                        <div>
                            <a href="{{ route('cart') }}" aria-label="Add to Cart">
                                <!-- <i class="fa-solid fa-cart-shopping"></i> -->
                                <i class="bi bi-cart3"></i>
                            </a>
                        </div>
                    @endif

                    <div class="app-navbar-item ms-1 " id="kt_header_user_menu_toggle">
                        <div class="cursor-pointer symbol symbol-35px "
                            data-kt-menu-trigger="{default: 'click', lg: 'hover'}" data-kt-menu-attach="parent"
                            data-kt-menu-placement="bottom-end">
                            @if (auth()->user()->profile->pic ?? '' == null)
                                <img src="{{ asset('website') }}/assets/media/avatars/blank.png" class="rounded-pill"
                                    alt="user" />
                            @else
                                <img alt="Logo"
                                    src="{{ asset('storage/uploads/users/' . auth()->user()->profile->pic) }}" />
                            @endif
                        </div>

                        <div class="menu menu-sub right-sidebar-menus menu-sub-dropdown menu-column menu-rounded menu-gray-800 menu-state-bg menu-state-color fw-semibold py-4 fs-6 w-275px"
                            data-kt-menu="true">
                            <div class="menu-item px-3">
                                <div class="menu-content d-flex align-items-center px-3">
                                    <div class="symbol symbol-50px me-5">
                                        @if (auth()->user()->profile->pic ?? '' == null)
                                            <img src="{{ asset('website') }}/assets/media/avatars/blank.png"
                                                class="rounded-pill" alt="user" />
                                        @else
                                            <img alt="Logo"
                                                src="{{ asset('storage/uploads/users/' . auth()->user()->profile->pic) }}" />
                                        @endif
                                    </div>
                                    <div class="d-flex flex-column">
                                        <div class="fw-bold d-flex align-items-center fs-5">{{ Auth::user()->name ?? '' }}
                                        </div>
                                        <a href="#"
                                            class="fw-semibold deep-blue fs-7">{{ Auth::user()->email ?? '' }}</a>
                                    </div>
                                </div>
                            </div>
                            <div class="separator my-2"></div>
                            @if (auth()->check() &&
                                    auth()->user()->hasAnyRole(['individual', 'business', 'admin']))
                                <div class="menu-item px-3">
                                    <a href="{{ route('home') }}" class="menu-link px-5">Dashboard</a>
                                </div>
                            @endif
                            <div class="menu-item px-3">
                                <a href="{{ route('profile_setting') }}" class="menu-link px-5">Profile</a>
                            </div>
                            <div class="separator my-2"></div>
                            <div class="menu-item px-3">
                                <a href="{{ url('logout') }}" class="menu-link px-5 logout">Logout</a>
                            </div>
                        </div>
                    </div>
                @else
                    <a href="{{ url('register') }}" class="button button1 login-btn">Register / Login</a>
                @endauth

            </div>
        </div>
    </nav>

    <div class="bg-white border-top header-items">
        <div class="container">
            <ul class="nav justify-content-start py-4 gap-7">
                <li class="nav-item"><a
                        class="nav-link fs-14 noraml sora semi_bold header-active    @if (request()->is('/')) active @endif"
                        href="{{ url('/') }}">Home</a></li>

                <li class="nav-item dropdown mega_menu position-static">
                    <a class="nav-link dropdown-toggle fs-14 semi_bold sora header-active  @if (request()->is('services')) active @endif"
                        href="#!" role="button" data-bs-toggle="dropdown" aria-expanded="false"
                        id="servicesLink">
                        Services
                    </a>
                    <div class="dropdown-menu service-drop-down px-3 p-0 rounded-0 shadow-none start-0">
                        {{-- Service Category and subcategory --}}
                        <x-navbar-service-component />
                        {{-- Service Category and subcategory  end --}}
                    </div>
                </li>

                <li class="nav-item dropdown mega_menu position-static">
                    <a class="nav-link dropdown-toggle fs-14 header-active  semi_bold sora  @if (request()->is('professional')) active @endif"
                        href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false"
                        id="professionalLink">
                        Professional
                    </a>
                    <div class="dropdown-menu professional-drop-down px-3 p-0 rounded-0 shadow-none start-0">
                        <div class="container">
                            <div class="row">
                                <div class="col-sm-3 ">
                                    <div class="d-flex align-items-start border-right py-6">
                                        <div class="nav flex-column nav-pills me-3" id="v-pills-tab" role="tablist"
                                            aria-orientation="vertical">
                                            <button class="nav-link mega_menu_tabs active"
                                                id="tab-personal-trainers-tab" data-bs-toggle="pill"
                                                data-bs-target="#tab-personal-trainers" type="button" role="tab"
                                                aria-controls="tab-personal-trainers" aria-selected="true">Personal
                                                Trainers</button>

                                            <button class="nav-link mega_menu_tabs" id="tab-makeup-artists-tab"
                                                data-bs-toggle="pill" data-bs-target="#tab-makeup-artists"
                                                type="button" role="tab" aria-controls="tab-makeup-artists"
                                                aria-selected="false">Makeup
                                                Artists</button>

                                            <button class="nav-link mega_menu_tabs" id="tab-nail-technicians-tab"
                                                data-bs-toggle="pill" data-bs-target="#tab-nail-technicians"
                                                type="button" role="tab" aria-controls="tab-nail-technicians"
                                                aria-selected="false">Nail
                                                Technicians</button>

                                            <button class="nav-link mega_menu_tabs" id="tab-massage-therapists-tab"
                                                data-bs-toggle="pill" data-bs-target="#tab-massage-therapists"
                                                type="button" role="tab" aria-controls="tab-massage-therapists"
                                                aria-selected="false">Massage Therapists</button>

                                            <button class="nav-link mega_menu_tabs" id="tab-beauty-consultants-tab"
                                                data-bs-toggle="pill" data-bs-target="#tab-beauty-consultants"
                                                type="button" role="tab" aria-controls="tab-beauty-consultants"
                                                aria-selected="false">Beauty Consultants</button>

                                            <button class="nav-link mega_menu_tabs" id="tab-wellness-coaches-tab"
                                                data-bs-toggle="pill" data-bs-target="#tab-wellness-coaches"
                                                type="button" role="tab" aria-controls="tab-wellness-coaches"
                                                aria-selected="false">Wellness Coaches</button>

                                            <button class="nav-link mega_menu_tabs" id="tab-dietitians-tab"
                                                data-bs-toggle="pill" data-bs-target="#tab-dietitians" type="button"
                                                role="tab" aria-controls="tab-dietitians"
                                                aria-selected="false">Dietitians &
                                                Nutritionists</button>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-sm-9">
                                    <div class="tab-content professional-header-tabs" id="v-pills-tabContent">
                                        @include('website.template.mega_menu_tabs.professional.personal-trainer-tab')
                                        @include('website.template.mega_menu_tabs.professional.makeup-artist-tab')
                                        @include('website.template.mega_menu_tabs.professional.nail-technicians-tab')
                                        @include('website.template.mega_menu_tabs.professional.massage-therapists-tab')
                                        @include('website.template.mega_menu_tabs.professional.beauty-consultants-tab')
                                        @include('website.template.mega_menu_tabs.professional.wellness-tab')
                                        @include('website.template.mega_menu_tabs.professional.dietitians-tab')
                                    </div>
                                </div>
                            </div>

                        </div>
                    </div>
                </li>

                @if (auth()->check() && auth()->user()->hasRole('customer'))
                    <li class="nav-item"><a
                            class="nav-link fs-14 noraml header-active sora semi_bold @if (request()->is('family_friends') || request()->is('add-friends') || request()->is('family_friends_details')) active @endif"
                            href="{{ route('family_friends') }}">Friends</a></li>
                    <li class="nav-item"><a
                            class="nav-link fs-14 noraml header-active sora semi_bold @if (request()->is('customer_booking')) active @endif"
                            href="{{ route('customer_booking') }}">My Booking</a></li>
                    <li class="nav-item"><a
                            class="nav-link fs-14 noraml header-active sora semi_bold @if (request()->is('customer_wallet')) active @endif"
                            href="{{ route('customer_wallet') }}">Wallet</a></li>
                @endif
                @guest()
                    <li class="nav-item ms-auto"><a class="nav-link blue-text fs-14 sora semi_bold"
                            href="{{ url('register') }}">Become a professional →</a></li>
                @endguest
            </ul>
        </div>
    </div>

</div>
