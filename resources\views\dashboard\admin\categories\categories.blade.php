@extends('dashboard.layout.master')
@section('content')
    <div id="kt_app_content" class="app-content flex-column-fluid customer_dashboard add-service">
        <div id="kt_app_content_container" class="app-container container padding-block">
            <div class="row row-gap-5">
                <div class="col-md-12 d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="sora black">Categories</h6>
                        <p class="fs-14 sora light-black m-0">Lorem ipsum dolor sit amet consectetur. </p>
                    </div>
                    @can('categories-create')
                        <a class="add-btn" id="add-category-button" data-bs-toggle="modal" data-bs-target="#add-category">
                            <i class="fa-solid fa-plus me-3"></i> Add Category
                        </a>
                    @endcan
                    @can('subcategories-create')
                        <a class="add-btn" id="add-subcategory-button" data-bs-toggle="modal"
                            data-bs-target="#add-sub-category">
                            <i class="fa-solid fa-plus me-3"></i> Add Sub Category
                        </a>
                    @endcan
                </div>
                <div class="col-lg-12">
                    <ul class="nav nav-pills mb-10" id="pills-tab" role="tablist">
                        @can('categories-list')
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active business-services" id="category-tab" data-bs-toggle="pill"
                                data-bs-target="#pills-category" type="button" role="tab"
                                aria-controls="pills-category" aria-selected="true">
                                Main Category
                            </button>
                        </li>
                        @endcan
                        @can('subcategories-list')
                        <li class="nav-item" role="presentation">
                            <button class="nav-link business-services" id="subcategory-tab" data-bs-toggle="pill"
                                data-bs-target="#pills-sub-category" type="button" role="tab"
                                aria-controls="pills-group-service" aria-selected="false">
                                Sub Category
                            </button>
                        </li>
                        @endcan
                    </ul>


                    <div class="tab-content" id="pills-tabContent">
                        <div class="tab-pane fade show active" id="pills-category" role="tabpanel"
                            aria-labelledby="category-tab" tabindex="0">
                            <div class="table-container">
                                <div class="table_top d-flex gap-3 align-items-center flex-wrap">
                                    <div class="search_box">
                                        <label for="customSearchInput">
                                            <i class="fas fa-search"></i>
                                        </label>
                                        <input class="search_input search" type="text" id="customSearchInput"
                                            placeholder="Search..." />
                                    </div>
                                    <!-- Date Picker -->
                                    <label for="datePicker" class="date_picker">
                                        <div class="date-picker-container">
                                            <i class="bi bi-calendar-event calender-icon"></i>
                                            <input type="text" name="datePicker" class="datePicker ms-3 w-200px">
                                            <i class="fa fa-chevron-down down-arrow  ms-9"></i>
                                        </div>
                                    </label>
                                </div>
                                <table id="responsiveTable" class="responsiveTable display w-100">
                                    <thead>
                                        <tr>
                                            <th></th>
                                            <th>CATEGRORY NAME</th>
                                            <th>SUB CATEGRORY</th>
                                            <th>DATE</th>
                                            <th></th>
                                            <th></th>
                                            <th></th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @forelse ($categories as $category)
                                            <tr>
                                                <td data-label="">
                                                    <div class="card  flex-row shadow-none p-0 gap-3">
                                                        <div class="card-header p-0 border-0 align-items-start">
                                                            <img src="{{ asset('website') . '/' . $category->image ?? '' }}"
                                                                class="h-80px w-80px rounded-3 object-fit-contain"
                                                                alt="card-image" />
                                                        </div>

                                                    </div>
                                                </td>
                                                <td data-label="CATEGRORY NAME">{{ $category->name ?? '' }}</td>
                                                <td data-label="SUB CATEGRORY">{{ $category->subcategories->count() ?? '' }}
                                                </td>
                                                <td data-label="DATE">{{ $category->created_at->format('d M, Y') ?? '' }}
                                                </td>
                                                <td data-label="">
                                                    <div class="toggle-container">
                                                        <label class="switch">
                                                            <!-- Dynamically set checked based on category status -->
                                                            <input type="checkbox" class="toggle-input category-toggle"
                                                                data-category-id="{{ $category->id }}"
                                                                {{ $category->status == 1 ? 'checked' : '' }}>
                                                            <span class="slider"></span>
                                                        </label>
                                                        <span
                                                            class="toggle-label">{{ $category->status == 1 ? 'Active' : 'Deactive' }}</span>
                                                    </div>
                                                </td>
                                                @can('subcategories-create')
                                                    <td data-label="">
                                                        <a class="purple-btn" data-bs-toggle="modal"
                                                            data-bs-target="#add-sub-category"
                                                            data-category-id="{{ $category->id }}">
                                                            <i class="fa-solid fa-plus me-3"></i> Add Sub Category
                                                        </a>
                                                    </td>
                                                @endcan
                                                <td data-label="">
                                                    <div class="dropdown">
                                                        <button class="drop-btn" type="button" id="dropdownMenuButton"
                                                            data-bs-toggle="dropdown" aria-expanded="false">
                                                            <i class="bi bi-three-dots-vertical"></i>
                                                        </button>
                                                        <ul class="dropdown-menu" aria-labelledby="dropdownMenuButton">
                                                            @can('categories-edit')
                                                                <li>
                                                                    <button
                                                                        class="dropdown-item complete fs-14 regular edit-category"
                                                                        type="button" data-id="{{ $category->ids }}">
                                                                        <i class="bi bi-check-circle complete-icon"></i>
                                                                        Edit
                                                                    </button>
                                                                </li>
                                                            @endcan
                                                            @can('categories-delete')
                                                                <li>
                                                                    <form
                                                                        action="{{ route('categories.destroy', $category->ids) }}"
                                                                        method="POST">
                                                                        @csrf
                                                                        @method('DELETE')
                                                                        <button class="dropdown-item cancel fs-14 regular"
                                                                            type="submit">
                                                                            <i class="fa-solid fa-xmark cancel-icon"></i>
                                                                            Delete
                                                                        </button>
                                                                    </form>
                                                                </li>
                                                            @endcan
                                                        </ul>
                                                    </div>
                                                </td>
                                            </tr>
                                        @empty
                                            <tr>
                                                <td colspan="6" class="text-center">No Categories Found</td>
                                            </tr>
                                        @endforelse
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        <div class="tab-pane fade" id="pills-sub-category" role="tabpanel"
                            aria-labelledby="subcategory-tab" tabindex="1">

                            <div class="table-container">
                                <div class="table_top d-flex gap-3 align-items-center flex-wrap">
                                    <div class="search_box">
                                        <label for="customSearchInput">
                                            <i class="fas fa-search"></i>
                                        </label>
                                        <input class="search_input search" type="text" id="customSearchInput"
                                            placeholder="Search..." />
                                    </div>
                                    <!-- Date Picker -->
                                    <label for="datePicker" class="date_picker">
                                        <div class="date-picker-container">
                                            <i class="bi bi-calendar-event calender-icon"></i>
                                            <input type="text" name="datePicker" class="datePicker ms-3 w-200px ">
                                            <i class="fa fa-chevron-down down-arrow ms-9"></i>
                                        </div>
                                    </label>
                                </div>
                                <table id="responsiveTable" class="responsiveTable display w-100">
                                    <thead>
                                        <tr>
                                            <th></th>
                                            <th>SUB CATEGRORY NAME</th>
                                            <th>MAIN CATEGRORY</th>
                                            <th>DATE</th>
                                            <th></th>
                                            <th></th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @forelse ($subcategories as $subcategory)
                                            <tr>
                                                <td data-label="">
                                                    <div class="card  flex-row shadow-none p-0 gap-3">
                                                        <div class="card-header p-0 border-0 align-items-start">
                                                            <img src="{{ asset('website') . '/' . $subcategory->image ?? '' }}"
                                                                class="h-80px w-80px rounded-3 object-fit-contain"
                                                                alt="card-image" />
                                                        </div>
                                                    </div>
                                                </td>
                                                <td data-label="SUB CATEGRORY NAME">{{ $subcategory->name ?? '' }}</td>
                                                <td data-label="MAIN CATEGRORY">
                                                    <p class="light-blue-badge">
                                                        {{ $subcategory->category->name ?? '' }}
                                                    </p>
                                                </td>
                                                <td data-label="DATE">
                                                    {{ $subcategory->created_at->format('d M, Y') ?? '' }}
                                                </td>
                                                <td data-label="">
                                                    <div class="toggle-container">
                                                        <label class="switch">
                                                            <!-- Dynamically set checked based on subcategory status -->
                                                            <input type="checkbox" class="toggle-input subcategory-toggle"
                                                                data-subcategory-id="{{ $subcategory->id }}"
                                                                {{ $subcategory->status == 1 ? 'checked' : '' }}>
                                                            <span class="slider"></span>
                                                        </label>
                                                        <span
                                                            class="toggle-label">{{ $subcategory->status == 1 ? 'Active' : 'Deactive' }}</span>
                                                    </div>
                                                </td>
                                                <td data-label="">
                                                    <div class="dropdown">
                                                        <button class="drop-btn" type="button" id="dropdownMenuButton"
                                                            data-bs-toggle="dropdown" aria-expanded="false">
                                                            <i class="bi bi-three-dots-vertical"></i>
                                                        </button>
                                                        <ul class="dropdown-menu" aria-labelledby="dropdownMenuButton">
                                                            @can('subcategories-edit')
                                                                <li>
                                                                    <button
                                                                        class="dropdown-item complete fs-14 regular edit-subcategory"
                                                                        type="button" data-id="{{ $subcategory->ids }}">
                                                                        <i class="bi bi-check-circle complete-icon"></i>
                                                                        Edit
                                                                    </button>
                                                                </li>
                                                            @endcan
                                                            @can('subcategories-delete')
                                                                <li>
                                                                    <form
                                                                        action="{{ route('subcategories.destroy', $subcategory->ids) }}"
                                                                        method="POST">
                                                                        @csrf
                                                                        @method('DELETE')
                                                                        <button class="dropdown-item cancel fs-14 regular"
                                                                            type="submit">
                                                                            <i class="fa-solid fa-xmark cancel-icon"></i>
                                                                            Delete
                                                                        </button>
                                                                    </form>
                                                                </li>
                                                            @endcan
                                                        </ul>
                                                    </div>
                                                </td>
                                            </tr>
                                        @empty
                                            <tr>
                                                <td colspan="6" class="text-center">No Categories Found</td>
                                            </tr>
                                        @endforelse
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    @include('dashboard.admin.categories.modal.add-category-modal')
    @include('dashboard.admin.categories.modal.edit-category-modal')
    @include('dashboard.admin.categories.modal.add-sub-category-modal')
    @include('dashboard.admin.categories.modal.edit-sub-category-modal')
@endsection
@push('js')
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.7.1/jquery.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.2.1/jquery.min.js"></script>
    <script>
        // Categories data for JavaScript lookup
        const categoriesData = {
            @foreach($activeCategories as $category)
                {{ $category->id }}: "{{ $category->name }}",
            @endforeach
        };
    </script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-validate/1.19.3/jquery.validate.min.js"></script>
    <script>
        $(document).ready(function() {
            $("#categoryForm").validate({
                rules: {
                    avatar: {
                        required: true
                    },
                    alt_tag: {
                        required: true
                    },
                    image_description: {
                        required: true
                    },
                    name: {
                        required: true
                    },
                    description: {
                        required: true
                    }
                },
                messages: {
                    avatar: {
                        required: "Please upload an image"
                    },
                    alt_tag: {
                        required: "Please enter alt tag"
                    },
                    image_description: {
                        required: "Please enter image description"
                    },
                    name: {
                        required: "Please enter category name"
                    },
                    description: {
                        required: "Please enter description"
                    }
                },
                submitHandler: function(form) {
                    form.submit();
                },
            });
        });
    </script>

    <script>
        $(document).ready(function() {
            $('.edit-category').click(function() {
                var categoryId = $(this).data('id');
                $.ajax({
                    url: '/categories/' + categoryId + '/edit',
                    method: 'GET',
                    success: function(data) {
                        $('#categoryId').val(data.ids);
                        $('#cat_name').val(data.name);
                        $('#cat_status').val(data.status);
                        $('#cat_image_description').val(data.image_description);
                        $('#cat_description').val(data.description);
                        $('#cat_alt_tag').val(data.alt_tag);
                        if (data.image) {
                            var baseImageUrl = $('meta[name="asset-url"]').attr('content') ||
                                '/website';
                            var imageUrl = baseImageUrl + '/' + data.image;
                            var imageInput = $(
                                '#edit-category .image-input[data-kt-image-input="true"]');
                            var wrapper = imageInput.find('.image-input-wrapper');
                            wrapper.css('background-image', 'url(' + imageUrl + ')');
                            imageInput.removeClass('image-input-empty').addClass(
                                'image-input-changed');
                            imageInput.find('[data-kt-image-input-action="remove"]')
                                .removeClass('d-none');
                            imageInput.find('[data-kt-image-input-action="cancel"]')
                                .removeClass('d-none');
                        } else {
                            var imageInput = $('.image-input[data-kt-image-input="true"]');
                            imageInput.addClass('image-input-empty').removeClass(
                                'image-input-changed');
                            imageInput.find('.image-input-wrapper').css('background-image',
                                'none');
                        }
                        $('#edit-category').modal('show');
                    },
                });
            });

            $('#editCategoryForm').on('submit', function(e) {
                e.preventDefault();
                var categoryId = $('#categoryId').val();
                var formData = new FormData(this);
                $.ajax({
                    url: '/categories/' + categoryId,
                    type: 'POST',
                    data: formData,
                    processData: false,
                    contentType: false,
                    success: function(response) {
                        $('#edit-category').modal('hide');
                        Swal.fire({
                            icon: response.type,
                            title: response.title,
                            text: response.message
                        });
                        setTimeout(() => {
                            location.reload();
                        }, 1500);
                    },
                    error: function(xhr) {
                        alert('Update failed. Please try again.');
                    }
                });
            });

        });
    </script>

    <script>
        $(document).ready(function() {
            $(".category-toggle").on('change', function() {
                var isChecked = $(this).prop('checked');
                var categoryId = $(this).data('category-id');
                var newStatus = isChecked ? 1 : 0;

                $.ajax({
                    url: "{{ route('categories.update-status') }}",
                    type: 'POST',
                    data: {
                        _token: '{{ csrf_token() }}',
                        category_id: categoryId,
                        status: newStatus
                    },
                    success: function(response) {
                        $(this).next('.toggle-label').text(newStatus === 1 ? 'Active' :
                            'Deactive');
                        // alert('Category status updated successfully');
                    },
                    error: function(xhr, status, error) {
                        alert('An error occurred while updating the category status');
                    }
                });
            });


            $(".subcategory-toggle").on('change', function() {
                var isChecked = $(this).prop('checked');
                var subcategoryId = $(this).data('subcategory-id');
                var newStatus = isChecked ? 1 : 0;
                $.ajax({
                    url: "{{ route('subcategories.update-status') }}",
                    type: 'POST',
                    data: {
                        _token: '{{ csrf_token() }}',
                        subcategory_id: subcategoryId,
                        status: newStatus
                    },
                    success: function(response) {
                        $(this).next('.toggle-label').text(newStatus === 1 ? 'Active' :
                            'Deactive');
                        // alert('Subcategory status updated successfully');
                    },
                    error: function(xhr, status, error) {
                        alert('An error occurred while updating the subcategory status');
                    }
                });
            });
        });
    </script>

    <script>
        $(document).ready(function() {
            $("#subCategoryForm").validate({
                rules: {
                    avatar: {
                        required: true
                    },
                    alt_image: {
                        required: true
                    },
                    image_description: {
                        required: true
                    },
                    category_id: {
                        required: true
                    },
                    name: {
                        required: true
                    },
                    description: {
                        required: true
                    }
                },
                messages: {
                    avatar: {
                        required: "Please upload an image"
                    },
                    alt_image: {
                        required: "Please enter alt tag"
                    },
                    image_description: {
                        required: "Please enter image description"
                    },
                    category_id: {
                        required: "Please select category"
                    },
                    name: {
                        required: "Please enter category name"
                    },
                    description: {
                        required: "Please enter description"
                    }
                },
                submitHandler: function(form) {
                    form.submit();
                },
            });
        });
    </script>

    <script>
        $(document).ready(function() {
            // Handle Add Sub Category button click to pre-select category
            $('[data-bs-target="#add-sub-category"]').click(function() {
                var categoryId = $(this).data('category-id');
                if (categoryId) {
                    // Row-specific button: show text field, hide select dropdown
                    var categoryName = categoriesData[categoryId] || '';
                    $('#add_category_name').val(categoryName).show();
                    $('#add_category_id').val(categoryId);
                    $('#category_select').hide();
                } else {
                    // General button: show select dropdown, hide text field
                    $('#category_select').show().val('').trigger('change');
                    $('#add_category_name').hide().val('');
                    $('#add_category_id').val('');
                }
            });

            // Handle category selection from dropdown
            $('#category_select').on('change', function() {
                var selectedCategoryId = $(this).val();
                $('#add_category_id').val(selectedCategoryId);
            });

            // Reset form when modal is closed
            $('#add-sub-category').on('hidden.bs.modal', function() {
                $('#subCategoryForm')[0].reset();
                $('#add_category_name').val('').hide();
                $('#add_category_id').val('');
                $('#category_select').val('').trigger('change').show();

                // Reset image input
                var imageInput = $('.image-input[data-kt-image-input="true"]');
                imageInput.addClass('image-input-empty').removeClass('image-input-changed');
                imageInput.find('.image-input-wrapper').css('background-image', 'none');
                imageInput.find('[data-kt-image-input-action="remove"]').addClass('d-none');
                imageInput.find('[data-kt-image-input-action="cancel"]').addClass('d-none');
            });

            $('.edit-subcategory').click(function() {
                var subCategoryId = $(this).data('id');
                $.ajax({
                    url: '/subcategories/' + subCategoryId + '/edit',
                    method: 'GET',
                    success: function(data) {
                        $('#subCategoryId').val(data.ids);
                        $('#sub_name').val(data.name);
                        $('#sub_status').val(data.status);
                        $('#sub_image_description').val(data.image_description);
                        $('#sub_alt_tag').val(data.alt_tag);
                        $('#sub_category_name').val(data.category_name);
                        $('#sub_description').val(data.description);
                        $('#hiddenCategoryId').val(data.category_id);
                        if (data.image) {
                            var baseImageUrl = $('meta[name="asset-url"]').attr('content') ||
                                '/website';
                            var imageUrl = baseImageUrl + '/' + data.image;
                            var imageInput = $(
                                '#edit-sub-category .image-input[data-kt-image-input="true"]'
                            );
                            var wrapper = imageInput.find('.image-input-wrapper');
                            wrapper.css('background-image', 'url(' + imageUrl + ')');
                            imageInput.removeClass('image-input-empty').addClass(
                                'image-input-changed');
                            imageInput.find('[data-kt-image-input-action="remove"]')
                                .removeClass('d-none');
                            imageInput.find('[data-kt-image-input-action="cancel"]')
                                .removeClass('d-none');
                        } else {
                            var imageInput = $('.image-input[data-kt-image-input="true"]');
                            imageInput.addClass('image-input-empty').removeClass(
                                'image-input-changed');
                            imageInput.find('.image-input-wrapper').css('background-image',
                                'none');
                        }
                        $('#edit-sub-category').modal('show');
                    },
                });
            });

            $('#editSubCategoryForm').on('submit', function(e) {
                e.preventDefault();
                var subCategoryId = $('#subCategoryId').val();
                var formData = new FormData(this);
                $.ajax({
                    url: '/subcategories/' + subCategoryId,
                    type: 'POST',
                    data: formData,
                    processData: false,
                    contentType: false,
                    success: function(response) {
                        $('#edit-sub-category   ').modal('hide');
                        Swal.fire({
                            icon: response.type,
                            title: response.title,
                            text: response.message
                        });
                        // setTimeout(() => {
                        //     location.reload();
                        // }, 1500);
                    },
                    error: function(xhr) {
                        alert('Update failed. Please try again.');
                    }
                });
            });

            // Reset add category modal when opened
            $('#add-category').on('show.bs.modal', function() {
                // Reset form
                $('#categoryForm')[0].reset();

                // Reset image input
                var imageInput = $('#add-category .image-input[data-kt-image-input="true"]');
                var wrapper = imageInput.find('.image-input-wrapper');
                wrapper.css('background-image', '');
                imageInput.removeClass('image-input-changed').addClass('image-input-empty');
            });

            // Reset add sub category modal when opened
            $('#add-sub-category').on('show.bs.modal', function() {
                // Reset form
                $('#subCategoryForm')[0].reset();

                // Reset category fields - default to showing select dropdown
                $('#add_category_name').val('').hide();
                $('#add_category_id').val('');
                $('#category_select').val('').trigger('change').show();

                // Reset image input
                var imageInput = $('#add-sub-category .image-input[data-kt-image-input="true"]');
                var wrapper = imageInput.find('.image-input-wrapper');
                wrapper.css('background-image', '');
                imageInput.removeClass('image-input-changed').addClass('image-input-empty');
            });

        });
    </script>
@endpush
