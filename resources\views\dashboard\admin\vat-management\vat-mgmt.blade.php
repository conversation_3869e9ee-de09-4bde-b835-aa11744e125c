@extends('dashboard.layout.master')
@section('content')
    <div id="kt_app_content" class="app-content flex-column-fluid customer_dashboard add-service">
        <div id="kt_app_content_container" class="app-container container padding-block">
            <div class="row row-gap-5">
                <div class="col-md-12 d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="sora black">VAT Management</h6>
                        <p class="fs-14 sora light-black m-0">Lorem ipsum dolor sit amet consectetur. </p>
                    </div>
                </div>
                <div class="col-lg-12">
                    <div class="table-container">
                        <div class="table_top d-flex gap-3 align-items-center flex-wrap mb-5">
                            <div class="search_box">
                                <label for="customSearchInput">
                                    <i class="fas fa-search"></i>
                                </label>
                                <input class="search_input search" type="text" id="customSearchInput"
                                    placeholder="Search..." />
                            </div>

                            <!-- add-city btn -->
                            @can('vatmanagements-create')
                            <div class="search_box d-block ms-auto">
                                <a class="add-btn" data-bs-toggle="modal" data-bs-target="#add-country">
                                    <i class="fa-solid fa-plus me-3"></i> Add Country
                                </a>
                            </div>
                            @endcan

                        </div>
                        <table id="responsiveTable"
                            class="responsiveTable manage-holiday vat-managment-table display w-100">
                            <thead>
                                <tr>
                                    <th></th>
                                    <th></th>
                                    <th></th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach ($vatmanagements as $vatmanagement)
                                    <tr>
                                        <td data-label="COUNTRY NAME">{{ $vatmanagement->country->name ?? '' }}</td>
                                        <td data-label="VAT RATE (%)">{{ $vatmanagement->vat }}%</td>
                                        @can('vatmanagements-edit')
                                        <td data-label="">
                                            <a class="purple-btn edit-vatmanagement"
                                                data-id="{{ $vatmanagement->ids }}">
                                                <i class="fa-solid fa-pen me-3"></i> Edit VAT%
                                            </a>
                                        </td>
                                        @endcan
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    @include('dashboard.admin.vat-management.modal.add-country-modal')
    @include('dashboard.admin.vat-management.modal.edit-vat-modal')
@endsection
@push('js')
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.7.1/jquery.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.2.1/jquery.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-validate/1.19.3/jquery.validate.min.js"></script>
    <script>
        $(document).ready(function() {
            $("#vatForm").validate({
                rules: {
                    country_id: {
                        required: true
                    },
                    vat: {
                        required: true
                    }
                },
                messages: {
                    country_id: {
                        required: "Please select country"
                    },
                    vat: {
                        required: "Please enter vat"
                    }
                },
                submitHandler: function(form) {
                    form.submit();
                },
            });
        });
    </script>

    <script>
        $(document).ready(function() {

            var currentVatManagementId = null;

            $('.edit-vatmanagement').click(function(e) {
                e.preventDefault();
                var vatManagementId = $(this).data('id');
                currentVatManagementId = vatManagementId;
                $.ajax({
                    url: '/vatmanagements/' + vatManagementId + '/edit',
                    method: 'GET',
                    success: function(data) {
                        $('#edit_country').val(data.country_id);
                        $('#edit_country_name').val(data.country_name);
                        $('#edit_vat').val(data.vat);
                        $('#edit-vat-modal').modal('show');
                    },
                    error: function(xhr, status, error) {
                        console.error('Error fetching VAT management data:', error);
                        alert('Error loading data. Please try again.');
                    }
                })
            });

            $('#editVatForm').on('submit', function(e) {
                e.preventDefault();
                var formData = new FormData(this);
                formData.append('_method', 'PUT');
                $.ajax({
                    url: '/vatmanagements/' + currentVatManagementId,
                    type: 'POST',
                    data: formData,
                    processData: false,
                    contentType: false,
                    success: function(response) {
                        $('#edit-vat-modal').modal('hide');
                        Swal.fire({
                            icon: response.type,
                            title: response.title,
                            text: response.message
                        });
                        setTimeout(() => {
                            location.reload();
                        }, 1500);
                    },
                    error: function(xhr) {
                        alert('Update failed. Please try again.');
                    }
                });
            });
        });
    </script>
@endpush
