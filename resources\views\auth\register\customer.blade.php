@extends('layouts.app')
@push('css')
    <style>
        .error-input {
            border-color: #dc3545 !important;
            outline: none !important;
            box-shadow: 0 0 0 2px rgba(220, 53, 69, 0.25) !important;
        }

        .image-input.error-input {
            border-color: #dc3545 !important;
        }

        .image-input.error-input .image-input-wrapper {
            border-color: #dc3545 !important;
        }
    </style>
@endpush
@section('content')
    <div class="container professional-acc-form customer-registration">
        <div class="row justify-content-center">
            <div class="col-md-12 stepper-navigation">
                <div class="d-flex justify-content-between">
                    <i name="previous" value="ll"
                        class="fas fa-chevron-left previous action-button-previous opacity-0"></i>
                    <input type="button" name="next" class="next action-button" value="Continue" />
                </div>
            </div>

            <div class="col-md-8 mb-2 pt-20">
                <div class=" px-0 pt-4 pb-0 mt-3 mb-3">
                    <form id="acc-form" action="{{ route('register.customer') }}" method="post"
                        enctype="multipart/form-data">
                        @csrf
                        <div class="form-card frst-step">
                            <div class="container">
                                <div class="row">
                                    <div class="col-md-12 mb-10">
                                        <h2>Create a Customer Account</h2>
                                        <p>You're almost there! Create your new account for {{ auth()->user()->email }} by
                                            completing these details.</p>
                                    </div>

                                    <div class="col-md-12 mb-10">
                                        <div class="Image-input_holder mb-10">
                                            <div class="image-input image-input-empty" data-kt-image-input="true">
                                                <div class="image-input image-input-outline" data-kt-image-input="true">
                                                    <div class="image-input-wrapper w-125px h-125px"></div>
                                                    <label class="dark-green-btn fs-14 regular pt-9"
                                                        data-kt-image-input-action="change">
                                                        <span class="pe-3 fs-16 fw-600 mb-10 blue-text"> Upload Logo</span>
                                                        <input type="file" name="avatar" id="avatar"
                                                            accept=".png, .jpg, .jpeg" id="profileImage" />
                                                        <input type="hidden" name="profile_image" />
                                                        <p class="fs-24 medium pt-4 gray-text"> At least 500x500 px
                                                            recommended. JPG or PNG is allowed</p>
                                                    </label>

                                                    <a href="#!" class="light-green-btn fs-14 regular ms-5"
                                                        data-kt-image-input-action="cancel" data-bs-toggle="tooltip"
                                                        data-bs-dismiss="click" title="Cancel avatar"> <i
                                                            class="fas fa-times fa-5"></i> </a>

                                                    <a href="#!" class=" ms-5" data-kt-image-input-action="remove"><i
                                                            class="fas fa-trash-alt"></i> </a>
                                                </div>
                                            </div>
                                            <p class="image-error-msg mt-5" style="color: red; display: none;">Image
                                                required</p>
                                            @error('avatar')
                                                <span class="invalid-feedback" role="alert">
                                                    <strong>{{ $message }}</strong>
                                                </span>
                                            @enderror
                                        </div>
                                    </div>

                                    <div class="col-md-12 mb-6">
                                        <label for="fullname" class="fieldlabels">Full Name</label>
                                        <input type="text" name="fullname" id="fullname" value="{{ old('fullname') }}"
                                            placeholder="Enter your first name" id="fullname" />
                                        @error('fullname')
                                            <span class="invalid-feedback" role="alert">
                                                <strong>{{ $message }}</strong>
                                            </span>
                                        @enderror
                                    </div>

                                    <div class="col-md-12 mb-6">
                                        <label for="email" class="fieldlabels">Email Address </label>
                                        <input type="email" value="{{ auth()->user()->email }}" id="email" readonly />
                                        @error('email')
                                            <span class="invalid-feedback" role="alert">
                                                <strong>{{ $message }}</strong>
                                            </span>
                                        @enderror
                                    </div>

                                    <div class="col-md-12">
                                        <label for="phone" class="fieldlabels">Phone Number</label>
                                        <input class="w-100 " type="tel" value="{{ old('phone') }}" id="phone"
                                            name="phone">
                                        @error('phone')
                                            <span class="invalid-feedback" role="alert">
                                                <strong>{{ $message }}</strong>
                                            </span>
                                        @enderror
                                    </div>

                                    <div class="col-md-12 mb-6">
                                        <label for="phone" class="fieldlabels">Services Preferences</label>
                                        <select id="services" name="services" class="form-select mb-2">
                                            <option selected disabled>Select service</option>
                                            @foreach ($services as $service)
                                                <option value="{{ $service->id }}"
                                                    {{ old('services') == $service->id ? 'selected' : '' }}>
                                                    {{ $service->name }}</option>
                                            @endforeach
                                        </select>
                                        @error('services')
                                            <span class="invalid-feedback" role="alert">
                                                <strong>{{ $message }}</strong>
                                            </span>
                                        @enderror
                                    </div>

                                    <div class="col-md-12 mb-6">
                                        <label for="website" class="fieldlabels">Password</label>
                                        <input type="password" class="form-control" name="password"
                                            placeholder="Enter password" id="password" />
                                        @error('password')
                                            <span class="invalid-feedback" role="alert">
                                                <strong>{{ $message }}</strong>
                                            </span>
                                        @enderror
                                    </div>

                                    <div class="col-md-12 mb-6">
                                        <label for="facebook" class="fieldlabels">Confirm Password</label>
                                        <input type="password" class="form-control" name="confirm_password"
                                            placeholder="Confirm your password" id="confirm_password" />
                                        @error('confirm_password')
                                            <span class="invalid-feedback" role="alert">
                                                <strong>{{ $message }}</strong>
                                            </span>
                                        @enderror
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('js')
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.7.1/jquery.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.2.1/jquery.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/cropperjs/1.5.13/cropper.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/dropzone/5.9.3/dropzone.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-validate/1.19.3/jquery.validate.min.js"></script>
    <script src="https://ajax.microsoft.com/ajax/jquery.validate/1.7/additional-methods.js"></script>

    <script src="https://cdn.jsdelivr.net/npm/intl-tel-input@17.0.19/build/js/intlTelInput.min.js"></script>

    <script>
        const input = document.querySelector("#phone"); // example selector
        window.intlTelInput(input, {
            initialCountry: "auto",
            geoIpLookup: function(callback) {
                fetch("https://ipinfo.io/json?token=1e240fc8539ff6")
                    .then((resp) => resp.json())
                    .then((resp) => {
                        const countryCode = resp && resp.country ? resp.country : "us";
                        callback(countryCode);
                    })
                    .catch(() => callback("us")); // fallback
            },
            utilsScript: "https://cdnjs.cloudflare.com/ajax/libs/intl-tel-input/17.0.8/js/utils.js"
        });
    </script>

    <script>
        const fileInput = document.querySelector('input[type="file"]');
        const wrapper = document.querySelector('.image-input-wrapper');
        const removeBtn = document.querySelector('[data-kt-image-input-action="remove"]');
        const cancelBtn = document.querySelector('[data-kt-image-input-action="cancel"]');
        const imageInput = document.querySelector('.image-input');

        fileInput.addEventListener('change', function(e) {
            const file = e.target.files[0];

            if (file) {
                const reader = new FileReader();

                reader.onload = function(event) {
                    wrapper.style.backgroundImage = `url('${event.target.result}')`;
                    imageInput.classList.remove('image-input-empty');
                };

                reader.readAsDataURL(file);
            }
        });

        // Remove action
        removeBtn.addEventListener('click', function() {
            wrapper.style.backgroundImage = '';
            fileInput.value = '';
            imageInput.classList.add('image-input-empty');
            // Set hidden input for backend if needed
            document.querySelector('input[name="avatar_remove"]').value = '1';
        });

        // Optional: Cancel action (if you need to reset back to default image, add that logic)
        cancelBtn.addEventListener('click', function() {
            wrapper.style.backgroundImage = '';
            fileInput.value = '';
            imageInput.classList.add('image-input-empty');
        });
    </script>

    <script>
        $(document).ready(function() {
            $("#acc-form").validate({
                rules: {
                    fullname: {
                        required: true,
                        minlength: 2
                    },
                    email: {
                        required: true,
                        email: true
                    },
                    phone: {
                        required: true,
                        minlength: 10
                    },
                    services: {
                        required: true,
                    },
                    password: {
                        required: true,
                        minlength: 8
                    },
                    confirm_password: {
                        required: true,
                        equalTo: "#password"
                    },
                    avatar: {
                        required: true
                    }
                },
                messages: {
                    fullname: {
                        required: "Please enter your full name",
                        minlength: "Your full name must consist of at least 2 characters"
                    },
                    email: {
                        required: "Please enter your email address",
                        email: "Please enter a valid email address"
                    },
                    phone: {
                        required: "Please enter your phone number",
                        minlength: "Please enter a valid phone number"
                    },
                    services: {
                        required: "Please select your services preferences"
                    },
                    password: {
                        required: "Please enter your password",
                        minlength: "Your password must consist of at least 8 characters"
                    },
                    confirm_password: {
                        required: "Please confirm your password",
                        equalTo: "Please enter the same password as above"
                    },
                    avatar: {
                        required: "Please upload your profile image"
                    }
                },
                submitHandler: function(form) {
                    form.submit();
                }
            });

            $(document).on('click', '.next.action-button', function(e) {
                e.preventDefault();
                $("#acc-form").submit();
            });
        });
    </script>
@endpush
