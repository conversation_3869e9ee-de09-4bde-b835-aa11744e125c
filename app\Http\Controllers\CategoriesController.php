<?php

namespace App\Http\Controllers;

use Illuminate\Support\Str;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\DB;
use App\Models\Category;
use App\Http\Requests\CategoryRequest;
use App\Models\SubCategory;
use App\Services\CategoryService;
use Illuminate\Http\Request;
use Spatie\Permission\Models\Permission;
use Illuminate\Support\Facades\Validator;

class CategoriesController extends Controller
{

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public $categoryService;
    function __construct()
    {
        $this->categoryService = new CategoryService();
        $this->middleware('permission:categories-list|categories-create|categories-edit|categories-delete', ['only' => ['index', 'store']]);
        $this->middleware('permission:categories-create', ['only' => ['create', 'store']]);
        $this->middleware('permission:categories-edit', ['only' => ['edit', 'update']]);
        $this->middleware('permission:categories-delete', ['only' => ['destroy']]);
        $this->middleware('permission:categories-list', ['only' => ['show']]);
    }
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Contracts\View\View
     */
    public function index()
    {
        $categories = Category::all();
        $subcategories = SubCategory::all();
        $activeCategories = Category::where('status', 1)->get();
        return view('dashboard.admin.categories.categories', [
            'categories' => $categories,
            'subcategories' => $subcategories,
            'activeCategories' => $activeCategories
        ]);
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Contracts\View\View
     */
    public function create()
    {
        return view('categories.create');
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  CategoryRequest  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function store(CategoryRequest $request)
    {
        $alreadyAdded = Category::where('name', $request->name)->exists();
        if ($alreadyAdded) {
            return back()->with([
                'type' => 'warning',
                'message' => 'You have already added this category',
                'title' => 'Already added'
            ]);
        }
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'description' => 'required',
            'avatar' => 'nullable|image|mimes:jpeg,png,jpg,gif,svg|max:2048',
            'image_description' => 'required',
            'alt_tag' => 'required',
        ]);
        if ($validator->fails()) {
            $errors = implode('<br>', $validator->errors()->all());
            return redirect()->back()->withInput()->with([
                'type' => 'error',
                'message' => $errors,
                'title' => 'Validation Errors',
            ]);
        }
        try {
            DB::beginTransaction();
            $categoryData = $validator->validated();
            if ($request->hasFile("avatar")) {
                $categoryData['image'] = $this->storeImage("category-images", $request->file('avatar'));
            }
            $categoryData['slug'] = Str::slug($request->input('name'));
            Category::create($categoryData);
            DB::commit();
            return redirect()->back()->with(["type" => "success", "title" => "Created", "message" => 'Category Created Successfully!!']);
        } catch (\Throwable $th) {
            DB::rollback();
            return redirect()->back()->with(["type" => "error", "message" => $th->getMessage(), "title" => "Error"]);
        }
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Contracts\View\View
     */
    public function show($id)
    {
        $category = Category::where('ids', $id)->firstOrFail();
        return view('categories.show', ['category' => $category]);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Contracts\View\View
     */
    public function edit($id)
    {
        $category = Category::where('ids', $id)->firstOrFail();
        return response()->json($category);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  CategoryRequest  $request
     * @param  int  $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function update(CategoryRequest $request, $id)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'description' => 'required',
            'avatar' => 'nullable|image|mimes:jpeg,png,jpg,gif,svg|max:2048',
            'image_description' => 'required',
            'alt_tag' => 'required',
        ]);
        if ($validator->fails()) {
            $errors = implode('<br>', $validator->errors()->all());
            return response()->json([
                'type' => 'error',
                'message' => $errors,
                'title' => 'Validation Errors',
            ]);
        }
        try {
            DB::beginTransaction();
            $category = Category::where('ids', $id)->firstOrFail();
            $categoryData = $validator->validated();
            if ($request->hasFile('avatar')) {
                $this->deleteImage($category->image);
                $categoryData['image'] = $this->storeImage('category-images', $request->file('avatar'));
            }
            if ($category->name !== $request->input('name')) {
                $categoryData['slug'] = Str::slug($request->input('name'));
            }
            $category->update($categoryData);
            DB::commit();
            return response()->json(["type" => "success", "message" => 'Category updated successfully', "title" => "Updated"]);
        } catch (\Throwable $th) {
            DB::rollback();
            return response()->json(["type" => "error", "message" => $th->getMessage(), "title" => "Error"]);
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function destroy($id)
    {
        $category = Category::where('ids', $id)->firstOrFail();
        $this->deleteImage($category->image);
        $category->delete();
        return redirect()->back()->with([
            'success' => true,
            'message' => 'Category deleted successfully'
        ]);
    }

    public function updateStatus(Request $request)
    {
        $category = Category::findOrFail($request->category_id);
        $category->status = $request->status;
        $category->save();

        return response()->json(['success' => true]);
    }
    function getSubCategories($category_id)
    {
        $subcategories = $this->categoryService->getSubCategories(ids: $category_id);
        return api_response(true, "Subcategories", $subcategories);
    }
}
