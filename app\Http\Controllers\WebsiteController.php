<?php

namespace App\Http\Controllers;

use App\Models\Category;
use App\Models\Home;
use App\Models\PrivacyAndTerm;
use App\Models\Service;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Artisan;

class WebsiteController extends Controller
{
    public function index()
    {
        $page = Home::with('details')->first();
        $categories = Category::where('status', 1)->get();
        return view('website.index', compact('page', 'categories'));
    }
    public function services()
    {
        $services = Service::get();

        return view('website.service', compact('services'));
    }
    public function professional()
    {
        return view('website.professional');
    }
    public function privacyPolicy()
    {
        $policies = PrivacyAndTerm::where('type', 'privacy')->get();
        return view('website.privacy-policy', compact('policies'));
    }
    public function terms()
    {
        $terms = PrivacyAndTerm::where('type', 'term')->get();
        return view('website.term', compact('terms'));
    }

    public function clear_all()
    {
        Artisan::call('route:clear');
        Artisan::call('cache:clear');
        Artisan::call('optimize:clear');
        Artisan::call('view:clear');
        Artisan::call('config:clear');
        return '<div style="text-align:center;"> <h1 style="text-align:center;">Cache and Config and permission cache are cleared.</h1><h4><a href="/">Go to home</a></h4></div>';
    }
}
