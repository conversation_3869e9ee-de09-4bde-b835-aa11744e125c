<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use App\Traits\HasUuid;

class DiscountCoupon extends Model
{
    use HasFactory, HasUuid;
    protected $table = 'discount_coupons';  
    protected $fillable = [
        'name',
        'coupon_code',
        'discount',
        'user_limit',
        'applies_to',
        'start_date',
        'end_date',
        'status'
    ];

    public function categories()
    {
        return $this->belongsToMany(Category::class, 'coupon_category', 'coupon_id', 'category_id');
    }

    public function services()
    {
        return $this->belongsToMany(Service::class, 'coupon_service', 'coupon_id', 'service_id');
    }
}
