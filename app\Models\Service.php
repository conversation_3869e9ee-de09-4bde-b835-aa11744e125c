<?php

namespace App\Models;

use App\Traits\HasUuid;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Service extends Model
{
    use HasFactory, HasUuid;

    protected $guarded = [
        'status',
    ];

    function category(){
        return $this->belongsTo(Category::class);
    }
    function subcategory(){
        return $this->belongsTo(SubCategory::class);
    }
    function user(){
        return $this->belongsTo(User::class);
    }
    
}
