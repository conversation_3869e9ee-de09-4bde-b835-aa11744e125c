<div class="container">
    <div class="row">
        <div class="col-sm-3 ">
            <div class="d-flex align-items-start border-right py-6">
                <div class="nav flex-column nav-pills me-3" id="v-pills-tab" role="tablist" aria-orientation="vertical">
                    @foreach ($categories as $category)
                        <button class="nav-link mega_menu_tabs {{ $loop->first ? 'active' : '' }} "
                            id="tab-{{ $category->ids }}-tab" data-bs-toggle="pill"
                            data-bs-target="#tab-{{ $category->ids }}" type="button" role="tab"
                            aria-controls="tab-{{ $category->ids }}" aria-selected="true">{{ $category->name }}</button>
                    @endforeach
                </div>
            </div>
        </div>

        <div class="col-sm-9">
            <div class="tab-content services-header-tabs" id="v-pills-tabContent">
                @foreach ($categories as $category)
                    <div class="tab-pane fade {{ $loop->first ? 'show active' : '' }} py-6"
                        id="tab-{{ $category->ids }}" role="tabpanel" aria-labelledby="tab-{{ $category->ids }}-tab">
                        <p class="fs-16 regular black">Fitness professionals helping clients
                            achieve their health goals</p>
                        <div class="row row-gap-5">
                            @foreach ($category->subcategories as $subcategory)
                                <div class="col-md-4">
                                    <div class="category-box active d-flex justify-content-between align-items-center">
                                        {{ $subcategory->name }}
                                        <span><i class="fa-solid fa-arrow-right arrow-icon"></i></span>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    </div>
                @endforeach
            </div>
        </div>
    </div>
</div>
