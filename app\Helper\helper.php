<?php

use Illuminate\Support\Facades\Storage;

function api_response(bool $status = true, string $message = "", mixed $data = null)
{
    return response()->json([
        'status' => $status,
        'message' => $message,
        'data' => $data,
    ]);
}

function setting()
{
    $setting = \App\Models\Setting::first();
    return $setting;
    // if (session()->has('setting')) {
    //     return session('setting');
    // } else {
    //     $setting = \App\Models\Setting::first();
    //     session(['setting' => $setting]);
    //     return $setting;
    // }
}

function storeImage($folderName, $file)
{
    try {
        return Storage::disk('website')->put($folderName, $file);
    } catch (\Exception $e) {
        return '';
    } //end trycatch.
} //end storeImage function.

function deleteImage($file)
{
    try {
        return Storage::disk('website')->delete($file);
    } catch (\Exception $e) {
        return '';
    } //end trycatch.
} //end storeImage function.