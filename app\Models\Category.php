<?php

namespace App\Models;
use App\Traits\HasUuid;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Support\Str;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Category extends Model
{
    use HasFactory , HasUuid;
    protected $fillable = [
        'image',
        'alt_tag',
        'image_description',
        'name',
        'slug',
        'status',
        'description'
    ];

    public function subcategories()
    {
        return $this->hasMany(SubCategory::class);
    }

    public function scopeActive($query){
        return $query->where('status', 1);
    }

     protected function name(): Attribute
    {
        return Attribute::make(
            // get: fn (string $value) => ucfirst($value),
            set: fn (string $value) => ucfirst($value),
        );
    }
}
