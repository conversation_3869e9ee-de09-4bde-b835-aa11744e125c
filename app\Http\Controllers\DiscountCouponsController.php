<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\DiscountCoupon;
use App\Http\Requests\DiscountCouponRequest;
use App\Models\Category;
use App\Models\Service;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Spatie\Permission\Models\Permission;

class DiscountCouponsController extends Controller
{

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    function __construct()
    {
        $this->middleware('permission:discountcoupons-list|discountcoupons-create|discountcoupons-edit|discountcoupons-delete', ['only' => ['index', 'store']]);
        $this->middleware('permission:discountcoupons-create', ['only' => ['create', 'store']]);
        $this->middleware('permission:discountcoupons-edit', ['only' => ['edit', 'update']]);
        $this->middleware('permission:discountcoupons-delete', ['only' => ['destroy']]);
        $this->middleware('permission:discountcoupons-list', ['only' => ['show']]);
    }
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Contracts\View\View
     */
    public function index()
    {
        $discountcoupons = DiscountCoupon::all();
        return view('dashboard.admin.discount coupons.index', compact('discountcoupons'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Contracts\View\View
     */
    public function create()
    {
        $categories = Category::all();
        $services = Service::all();
        return view('dashboard.admin.discount coupons.create&edit', compact('categories', 'services'));
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  DiscountCouponRequest  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function store(DiscountCouponRequest $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string',
            'coupon_code' => 'required|string|unique:discount_coupons',
            'discount' => 'required|integer',
            'user_limit' => 'required|integer',
            'applies_to' => 'required|in:category,service',
            'start_date' => 'required|date',
            'end_date' => 'required|date|after_or_equal:start_date',
        ]);
        if ($validator->fails()) {
            $errors = implode('<br>', $validator->errors()->all());
            return redirect()->back()->withInput()->with([
                'type' => 'error',
                'message' => $errors,
                'title' => 'Validation Errors',
            ]);
        }
        try {
            DB::beginTransaction();
            $discountcouponData = $validator->validated();
            $coupon = DiscountCoupon::create($discountcouponData);
            if ($request->applies_to === 'category' && $request->has('categories')) {
                $coupon->categories()->sync($request->categories);
            }
            if ($request->applies_to === 'service' && $request->has('services')) {
                $coupon->services()->sync($request->services);
            }
            DB::commit();
            return to_route('discount-coupons.index')->with(["type" => "success", "title" => "Created", "message" => 'Discount Coupon Created Successfully!!']);
        } catch (\Throwable $th) {
            DB::rollback();
            return to_route('discount-coupons.index')->with(["type" => "error", "message" => $th->getMessage(), "title" => "Error"]);
        }
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Contracts\View\View
     */
    public function show($id)
    {
        $discountcoupon = DiscountCoupon::where('ids', $id)->firstOrFail();
        return view('discountcoupons.show', ['discountcoupon' => $discountcoupon]);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Contracts\View\View
     */
    public function edit($id)
    {
        $discountcoupon = DiscountCoupon::where('ids', $id)->firstOrFail();
        $categories = Category::all();
        $services = Service::all();
        return view('dashboard.admin.discount coupons.create&edit', compact('discountcoupon', 'categories', 'services'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  DiscountCouponRequest  $request
     * @param  int  $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function update(DiscountCouponRequest $request, $id)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string',
            'coupon_code' => 'required|string|unique:discount_coupons',
            'discount' => 'required|integer',
            'user_limit' => 'required|integer',
            'applies_to' => 'required|in:category,service',
            'start_date' => 'required|date',
            'end_date' => 'required|date|after_or_equal:start_date',
        ]);
        if ($validator->fails()) {
            $errors = implode('<br>', $validator->errors()->all());
            return redirect()->back()->withInput()->with([
                'type' => 'error',
                'message' => $errors,
                'title' => 'Validation Errors',
            ]);
        }
        try {
            DB::beginTransaction();
            $discountcouponData = $validator->validated();
            $coupon = DiscountCoupon::where('ids', $id)->firstOrFail();
            $coupon->update($discountcouponData);
            $coupon->categories()->detach();
            $coupon->services()->detach();
            if ($request->applies_to === 'category' && $request->has('categories')) {
                $coupon->categories()->sync($request->categories);
            }
            if ($request->applies_to === 'service' && $request->has('services')) {
                $coupon->services()->sync($request->services);
            }
            DB::commit();
            return to_route('discount-coupons.index')->with(["type" => "success", "title" => "Created", "message" => 'Discount Coupon Updated Successfully!!']);
        } catch (\Throwable $th) {
            DB::rollback();
            return to_route('discount-coupons.index')->with(["type" => "error", "message" => $th->getMessage(), "title" => "Error"]);
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function destroy($id)
    {
        $discountcoupon = DiscountCoupon::where('ids', $id)->firstOrFail();
        $discountcoupon->delete();
        return redirect()->back()->with([
            'success' => true,
            'message' => 'Discount Coupon deleted successfully'
        ]);
    }

    /**
     * Check if coupon code already exists
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function checkCouponCode(Request $request)
    {
        $couponCode = $request->input('coupon_code');
        $currentId = $request->input('id');
        $query = DiscountCoupon::where('coupon_code', $couponCode);
        if ($currentId) {
            $query->where('ids', '!=', $currentId);
        }
        $exists = $query->exists();
        return response()->json([
            'available' => !$exists
        ]);
    }

    public function updateStatus(Request $request)
    {
        $discountcoupon = DiscountCoupon::findOrFail($request->discountcoupon_id);
        $discountcoupon->status = $request->status;
        $discountcoupon->save();

        return response()->json(['success' => true]);
    }
}
