@extends('dashboard.layout.master')
@section('content')
    <div id="kt_app_content" class="app-content flex-column-fluid customer_dashboard add-service">
        <div id="kt_app_content_container" class="app-container container padding-block">
            <div class="row row-gap-5">
                <div class="col-md-12 d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="sora black">Discount & Coupon </h6>
                        <p class="fs-14 sora light-black m-0">Lorem ipsum dolor sit amet consectetur. </p>
                    </div>
                    @can('discountcoupons-create')
                        <a href="{{ route('discount-coupons.create') }}" class="add-btn">
                            <i class="fa-solid fa-plus me-3"></i> Add Coupon
                        </a>
                    @endcan
                </div>
                <div class="col-lg-12">
                    <div class="table-container">
                        <div class="table_top d-flex gap-3 align-items-center flex-wrap">
                            <div class="search_box">
                                <label for="customSearchInput">
                                    <i class="fas fa-search"></i>
                                </label>
                                <input class="search_input search" type="text" id="customSearchInput"
                                    placeholder="Search..." />
                            </div>
                            <!-- Select with dots -->
                            <div class="dropdown search_box select-box">
                                <button
                                    class="dropdown-toggle search_input status-dropdown-button d-flex align-items-center justify-content-start"
                                    type="button" data-bs-toggle="dropdown" aria-expanded="false">
                                    <span><span class="dot"></span>
                                        All</span>
                                </button>
                                <ul class="dropdown-menu">
                                    <li><a class="dropdown-item dropdown-status" href="#" data-label="All"
                                            data-color="#4B5563"><span class="dot all"></span>
                                            All</a></li>
                                    <li><a class="dropdown-item dropdown-status" href="#" data-label="Complete"
                                            data-color="#10B981"><span class="dot completed"></span>
                                            Active</a></li>
                                    <li><a class="dropdown-item dropdown-status" href="#" data-label="Canceled"
                                            data-color="#EF4444"><span class="dot cancelled-dot"></span>
                                            Deactive</a></li>
                                </ul>
                            </div>
                            @if (auth()->check() && auth()->user()->hasAnyRole('individual', 'business'))
                                <!-- Date Picker -->
                                <label for="datePicker" class="date_picker">
                                    <div class="date-picker-container">
                                        <i class="bi bi-calendar-event calender-icon"></i>
                                        <input type="text" name="datePicker" class="datePicker w-200px ms-3">
                                        <i class="fa fa-chevron-down down-arrow ms-9"></i>
                                    </div>
                                </label>
                                </label>
                            @endif
                        </div>
                        <table id="responsiveTable" class="responsiveTable display w-100">
                            <thead>
                                <tr>
                                    <th>Coupon name</th>
                                    <th>Coupon code</th>
                                    <th>Discount</th>
                                    <th>Usage</th>
                                    <th>Start & End date</th>
                                    <th>Action</th>
                                    <th></th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse ($discountcoupons as $discountcoupon)
                                    <tr>
                                        <td data-label="Coupon name">{{ $discountcoupon->name ?? '' }}</td>
                                        <td data-label="Coupon code">{{ $discountcoupon->coupon_code ?? '' }}</td>
                                        <td data-label="Discount">{{ $discountcoupon->discount ?? '' }}%</td>
                                        <td data-label="Usage">50/{{ $discountcoupon->user_limit ?? '' }}</td>
                                        <td data-label="Start & End date">
                                            {{ $discountcoupon->start_date ?? '' }}
                                            -
                                            {{ $discountcoupon->end_date ?? '' }}
                                        </td>
                                        <td data-label="">
                                            <div class="toggle-container">
                                                <label class="switch">
                                                    <input type="checkbox" class="toggle-input discount-toggle"
                                                        data-discount-id="{{ $discountcoupon->id }}"
                                                        {{ $discountcoupon->status == 1 ? 'checked' : '' }}>
                                                    <span class="slider"></span>
                                                </label>
                                                <span
                                                    class="toggle-label">{{ $discountcoupon->status == 1 ? 'Active' : 'Deactive' }}</span>
                                            </div>
                                        </td>
                                        <td data-label="">
                                            <div class="dropdown">
                                                <button class="drop-btn" type="button" id="dropdownMenuButton"
                                                    data-bs-toggle="dropdown" aria-expanded="false">
                                                    <i class="bi bi-three-dots-vertical"></i>
                                                </button>
                                                <ul class="dropdown-menu" aria-labelledby="dropdownMenuButton">
                                                    @can('discountcoupons-edit')
                                                        <li>
                                                            <a class="dropdown-item complete fs-14 regular "
                                                                href="{{ route('discount-coupons.edit', $discountcoupon->ids) }}">
                                                                <i class="bi bi-check-circle complete-icon"></i>
                                                                Edit
                                                            </a>
                                                        </li>
                                                    @endcan
                                                    @can('discountcoupons-delete')
                                                        <li>
                                                            <form
                                                                action="{{ route('discount-coupons.destroy', $discountcoupon->ids) }}"
                                                                method="POST">
                                                                @csrf
                                                                @method('DELETE')
                                                                <button class="dropdown-item cancel fs-14 regular"
                                                                    type="submit">
                                                                    <i class="fa-solid fa-xmark cancel-icon"></i>
                                                                    Delete
                                                                </button>
                                                            </form>
                                                        </li>
                                                    @endcan
                                                </ul>
                                            </div>
                                        </td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="7" class="text-center">No Discount Coupons Found</td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
@push('js')
    <script>
        $(document).ready(function() {
            $(".discount-toggle").on('change', function() {
                var $toggle = $(this);
                var isChecked = $toggle.prop('checked');
                var discountId = $toggle.data('discount-id');
                var newStatus = isChecked ? 1 : 0;

                $.ajax({
                    url: "{{ route('discount-coupons.update-status') }}",
                    type: 'POST',
                    data: {
                        _token: '{{ csrf_token() }}',
                        discountcoupon_id: discountId,
                        status: newStatus
                    },
                    success: function(response) {
                        $toggle.closest('.toggle-container').find('.toggle-label').text(
                            newStatus === 1 ? 'Active' : 'Deactive');
                        // alert('Discount coupon status updated successfully');
                    },
                    error: function(xhr, status, error) {
                        alert('An error occurred while updating the status');
                        // Revert the toggle state on error
                        $toggle.prop('checked', !isChecked);
                    }
                });
            });
        });
    </script>
@endpush
