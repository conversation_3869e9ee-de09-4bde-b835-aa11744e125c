<div class="modal fade card-details" id="add-country" tabindex="-1" aria-labelledby="add-country" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header p-5">
                <h5 class="fs-15 semi_bold sora black"> Add a Country</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="vatForm" action="{{ route('vatmanagements.store') }}" method="POST">
                @csrf
                <div class="modal-body">
                    <div class="row row-gap-5">
                        <div class="col-md-12">
                            <label for="service" class="form-label form-input-labels">Select Country</label>
                            <select class="form-select form-select-field" id="service" name="country_id"
                                data-control="select2" data-placeholder="Select Country">
                                <option></option>
                                @foreach ($countries as $country)
                                    <option value="{{ $country->id }}"
                                        {{ old('country_id') == $country->id ? 'selected' : '' }}>
                                        {{ $country->name }}
                                    </option>
                                @endforeach
                            </select>
                        </div>

                        <div class="col-md-12">
                            <label for="discount" class="form-label form-input-labels">VAT </label>
                            <div class="input-group">
                                <span class="input-group-text" id="basic-addon1">%</span>
                                <input type="text" class="form-control form-inputs-field" placeholder="Enter VAT %"
                                    id="vat" name="vat" value="{{ old('vat') }}" />
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer border-0 ">
                    <button type="button" class="cancel-btn " data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="save-btn">Add</button>
                </div>
            </form>
        </div>
    </div>
</div>
