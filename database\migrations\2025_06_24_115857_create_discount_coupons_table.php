<?php

use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\Schema;

class CreateDiscountCouponsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('discount_coupons', function (Blueprint $table) {
            $table->id();
			$table->string('name')->nullable();
			$table->string('ids')->nullable();
			$table->string('coupon_code')->nullable();
			$table->integer('discount')->nullable();
			$table->integer('user_limit')->nullable();
			$table->enum('applies_to', ['category', 'service']);
			$table->string('start_date')->nullable();
			$table->string('end_date')->nullable();
            $table->integer('status')->default(1);
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('discount_coupons');
    }
}
