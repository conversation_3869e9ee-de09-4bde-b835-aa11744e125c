<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use App\Traits\HasUuid;
use Illuminate\Database\Eloquent\Model;

class VatManagement extends Model
{
    use HasFactory,HasUuid;
    protected $table = 'vat_managements';
    protected $fillable = [
        'country_id',
        'ids',
        'vat',
        'status',
    ];

    public function country()
    {
        return $this->belongsTo(Country::class);
    }
}
