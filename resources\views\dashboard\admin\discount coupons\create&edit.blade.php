@extends('dashboard.layout.master')
@section('content')
    <div id="kt_app_content" class="app-content flex-column-fluid customer_dashboard add-coupon padding-block">
        <div id="kt_app_content_container" class="app-container container padding-block">
            <div class="row row-gap-5">
                <div class="col-md-12 breadcrumbs">
                    <h6 class="sora black">{{ isset($discountcoupon) ? 'Edit' : 'Add' }} Discount Coupon</h6>
                    <p class="fs-14 sora light-black m-0">Discount & Coupon <span class="mx-3"><i
                                class="fa-solid fa-chevron-right right-arrow"></i></span>
                        {{ isset($discountcoupon) ? 'Edit' : 'Add' }} Discount Coupon </p>

                </div>
                <div class="col-md-12">
                    <form id="discountCouponForm"
                        action="{{ isset($discountcoupon) ? route('discount-coupons.update', $discountcoupon->ids) : route('discount-coupons.store') }}"
                        method="POST" class="form-add-services">
                        @csrf
                        @if (isset($discountcoupon))
                            @method('PUT')
                        @endif
                        <div class="row row-gap-5">
                            <div class="col-md-6">
                                <label for="coupon-name" class="form-label form-input-labels">Coupon Name</label>
                                <input type="text" class="form-control form-inputs-field" placeholder="Enter coupon name"
                                    id="name" name="name"
                                    value="{{ isset($discountcoupon) ? $discountcoupon->name : old('name') }}">
                            </div>
                            <div class="col-md-6">
                                <label for="coupon-code" class="form-label form-input-labels">Coupon Code</label>
                                <input type="text" class="form-control form-inputs-field" placeholder="Enter coupon code"
                                    id="coupon-code" name="coupon_code"
                                    value="{{ isset($discountcoupon) ? $discountcoupon->coupon_code : old('coupon_code') }}">
                            </div>
                            <div class="col-md-6">
                                <label for="discount" class="form-label form-input-labels">Discount </label>
                                <div class="input-group">
                                    <span class="input-group-text" id="basic-addon1">%</span>
                                    <input type="number" min="1" class="form-control form-inputs-field"
                                        placeholder="Enter discount" id="discount" name="discount"
                                        value="{{ isset($discountcoupon) ? $discountcoupon->discount : old('discount') }}" />
                                </div>
                            </div>
                            <div class="col-md-6">
                                <label for="user-limit" class="form-label form-input-labels">User Limit</label>
                                <input type="number" min="1" class="form-control form-inputs-field" placeholder="Enter user limit"
                                    id="user-limit" name="user_limit"
                                    value="{{ isset($discountcoupon) ? $discountcoupon->user_limit : old('user_limit') }}">
                            </div>
                            <!-- Radio Buttons -->
                            <div class="col-md-12">
                                <label for="category-service" class="form-label form-input-labels">Choose Category or
                                    Services</label>
                                <div class="d-flex gap-4 mb-3">
                                    <label for="filter-by-category" class="d-flex gap-2 align-items-center">
                                        <input class="form-check-input" type="radio" name="applies_to" value="category"
                                            id="filter-by-category"
                                            {{ (isset($discountcoupon) ? $discountcoupon->applies_to : old('applies_to', 'category')) == 'category' ? 'checked' : '' }}>
                                        <span>Category</span>
                                    </label>
                                    <label for="filter-by-service" class="d-flex gap-2 align-items-center">
                                        <input class="form-check-input" type="radio" name="applies_to" value="service"
                                            id="filter-by-service"
                                            {{ (isset($discountcoupon) ? $discountcoupon->applies_to : old('applies_to')) == 'service' ? 'checked' : '' }}>
                                        <span>Services</span>
                                    </label>
                                </div>
                                <!-- Dropdown Fields -->
                                <div class="row row-gap-5">
                                    <!-- Category Dropdown -->
                                    <div class="col-md-12 form-hide-box" id="category-dropdown-wrapper">
                                        <select class="form-select form-select-field" id="category-select"
                                            name="categories[]" data-control="select2" data-placeholder="Select Categories"
                                            multiple>
                                            @foreach ($categories as $category)
                                                <option value="{{ $category->id }}"
                                                    {{ isset($discountcoupon) && $discountcoupon->categories->contains('id', $category->id) ? 'selected' : (in_array($category->id, old('categories', [])) ? 'selected' : '') }}>
                                                    {{ $category->name }}
                                                </option>
                                            @endforeach
                                        </select>
                                    </div>

                                    <!-- Service Dropdown -->
                                    <div class="col-md-12 form-hide-box" id="service-dropdown-wrapper">
                                        <select class="form-select form-select-field" id="service-select" name="services[]"
                                            data-control="select2" data-placeholder="Select Services" multiple>
                                            @foreach ($services as $service)
                                                <option value="{{ $service->id }}"
                                                    {{ isset($discountcoupon) && $discountcoupon->services->contains('id', $service->id) ? 'selected' : (in_array($service->id, old('services', [])) ? 'selected' : '') }}>
                                                    {{ $service->name }}
                                                </option>
                                            @endforeach
                                        </select>
                                    </div>
                                </div>

                            </div>
                            <div class="col-md-6">
                                <label for="staff-member" class="form-label form-input-labels">Staff Member</label>
                                <select class="form-select form-select-field" id="staff-member" name="staff-member"
                                    data-control="select2" data-placeholder="All Staff Member">
                                    <option></option>
                                    <option value="1">Option 1</option>
                                    <option value="2">Option 2</option>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label for="start_date" class="form-label form-input-labels">Start Date</label>
                                <div class="flatpickr input-group form-control form-inputs-field" data-wrap="true">
                                    <input type="text" id="start_date" class="" placeholder="Select start date"
                                        data-input name="start_date"
                                        value="{{ isset($discountcoupon) ? $discountcoupon->start_date : old('start_date') }}">
                                    <button class="input-button calender-button" type="button" title="toggle"
                                        data-toggle>
                                        <i class="fa-regular fa-calendar"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <label for="end_date" class="form-label form-input-labels">End Date</label>
                                <div class="flatpickr input-group form-control form-inputs-field" data-wrap="true">
                                    <input type="text" id="end_date" class="" placeholder="Select end date"
                                        data-input name="end_date"
                                        value="{{ isset($discountcoupon) ? $discountcoupon->end_date : old('end_date') }}">
                                    <button class="input-button calender-button" type="button" title="toggle"
                                        data-toggle>
                                        <i class="fa-regular fa-calendar"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="col-md-12 mt-6">
                                <button type="submit" class="add-btn">
                                    {{ isset($discountcoupon) ? 'Update' : 'Add' }}
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
@endsection
@push('js')
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-validate/1.19.3/jquery.validate.min.js"></script>
    <style>
        .error {
            color: red !important;
            font-weight: bold !important;
            font-size: 12px;
            margin-top: 5px;
            display: block;
        }
        .form-control.error, .form-select.error {
            border-color: red !important;
        }
    </style>
    <!-- jQuery Script -->
    <script>
        $(document).ready(function() {
            function toggleFilterFields(value) {
                if (value === 'category') {
                    $('#category-dropdown-wrapper').show();
                    $('#service-dropdown-wrapper').hide();
                } else {
                    $('#category-dropdown-wrapper').hide();
                    $('#service-dropdown-wrapper').show();
                }
            }

            // Initial load - check current selection
            toggleFilterFields($('input[name="applies_to"]:checked').val());

            // On radio change
            $('input[name="applies_to"]').on('change', function() {
                toggleFilterFields(this.value);
            });

            // Custom validation methods
            $.validator.addMethod("dateGreaterThan", function(value, element, params) {
                if (!value || !$(params).val()) return true;
                var startDate = new Date($(params).val());
                var endDate = new Date(value);
                return endDate >= startDate;
            }, "End date must be after or equal to start date");

            $.validator.addMethod("categoryRequired", function(value, element) {
                if ($('input[name="applies_to"]:checked').val() === 'category') {
                    return $('#category-select').val() && $('#category-select').val().length > 0;
                }
                return true;
            }, "Please select at least one category");

            $.validator.addMethod("serviceRequired", function(value, element) {
                if ($('input[name="applies_to"]:checked').val() === 'service') {
                    return $('#service-select').val() && $('#service-select').val().length > 0;
                }
                return true;
            }, "Please select at least one service");

            // Check if coupon code already exists
            $.validator.addMethod("couponCodeUnique", function(value, element) {
                var isValid = false;
                var currentId = "{{ isset($discountcoupon) ? $discountcoupon->ids : '' }}";

                $.ajax({
                    url: "{{ route('discountcoupons.check-coupon-code') }}",
                    type: "POST",
                    async: false,
                    data: {
                        coupon_code: value,
                        id: currentId,
                        _token: "{{ csrf_token() }}"
                    },
                    success: function(response) {
                        isValid = response.available;
                    }
                });
                return isValid;
            }, "This coupon code already exists");

            // Form validation
            $(".form-add-services").validate({
                errorClass: "error",
                errorElement: "span",
                errorPlacement: function(error, element) {
                    if (element.attr("name") == "categories[]") {
                        error.insertAfter("#category-select");
                    } else if (element.attr("name") == "services[]") {
                        error.insertAfter("#service-select");
                    } else if (element.closest('.flatpickr').length) {
                        error.insertAfter(element.closest('.flatpickr'));
                    } else {
                        error.insertAfter(element);
                    }
                },
                rules: {
                    name: {
                        required: true,
                        minlength: 2
                    },
                    coupon_code: {
                        required: true,
                        minlength: 3,
                        couponCodeUnique: true
                    },
                    discount: {
                        required: true,
                        number: true,
                        min: 1,
                        max: 100
                    },
                    user_limit: {
                        required: true,
                        number: true,
                        min: 1
                    },
                    start_date: {
                        required: true
                    },
                    end_date: {
                        required: true,
                        dateGreaterThan: "#start_date"
                    },
                    "categories[]": {
                        categoryRequired: true
                    },
                    "services[]": {
                        serviceRequired: true
                    }
                },
                messages: {
                    name: {
                        required: "Coupon name is required",
                        minlength: "Coupon name must be at least 2 characters"
                    },
                    coupon_code: {
                        required: "Coupon code is required",
                        minlength: "Coupon code must be at least 3 characters"
                    },
                    discount: {
                        required: "Discount percentage is required",
                        number: "Please enter a valid number",
                        min: "Discount must be at least 1%",
                        max: "Discount cannot exceed 100%"
                    },
                    user_limit: {
                        required: "User limit is required",
                        number: "Please enter a valid number",
                        min: "User limit must be at least 1"
                    },
                    start_date: {
                        required: "Start date is required"
                    },
                    end_date: {
                        required: "End date is required"
                    }
                },
                submitHandler: function(form) {
                    form.submit();
                }
            });

            // Trigger validation when radio buttons change
            $('input[name="applies_to"]').on('change', function() {
                $('#category-select, #service-select').valid();
            });

            // Trigger validation when select values change
            $('#category-select, #service-select').on('change', function() {
                $(this).valid();
            });
        });
    </script>
@endpush
