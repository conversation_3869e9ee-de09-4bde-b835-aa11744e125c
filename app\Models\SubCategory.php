<?php

namespace App\Models;
use App\Traits\HasUuid;
use Illuminate\Support\Str;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class SubCategory extends Model
{
    use HasFactory, HasUuid;
    protected $fillable = [
        'image',
        'alt_tag',
        'image_description',
        'category_id',
        'name',
        'slug',
        'status',
        'description'
    ];

    public function category()
    {
        return $this->belongsTo(Category::class);
    }
}
