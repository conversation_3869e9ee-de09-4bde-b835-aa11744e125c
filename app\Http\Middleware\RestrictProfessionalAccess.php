<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class RestrictProfessionalAccess
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle($request, Closure $next)
    {
        $user = auth()->user();

        if ($user && $user->hasRole('professional') && $user->registration_completed == 1) {
            $allowedRoutes = [
                'setting',
                'subscriptions.index',
                'profile_setting',
                'payment.subscription',
                'subscription.success',
                'subscription.failed',
                'stripe.webhook',
            ];
            $currentRouteName = $request->route()->getName();
            if (!in_array($currentRouteName, $allowedRoutes)) {
                return redirect()->route('subscriptions.index');
            }
        }
        return $next($request);
    }
}
